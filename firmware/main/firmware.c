#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/i2c.h"
#include "driver/uart.h"
#include "esp_log.h"
#include "esp_err.h"
#include <stdio.h>

// I2C Bus 0 Configuration (Sensor 1)
#define I2C_BUS_0 I2C_NUM_0
#define I2C_BUS_0_SDA_IO 15
#define I2C_BUS_0_SCL_IO 17

// I2C Bus 1 Configuration (Sensor 2)
#define I2C_BUS_1 I2C_NUM_1
#define I2C_BUS_1_SDA_IO 12
#define I2C_BUS_1_SCL_IO 14

// Common I2C Configuration
#define I2C_MASTER_FREQ_HZ 400000
#define I2C_MASTER_TIMEOUT_MS 1000

// UART Configuration
#define UART_PORT_NUM UART_NUM_0
#define UART_BAUD_RATE 115200
#define UART_TX_PIN 43
#define UART_RX_PIN 44
#define UART_BUF_SIZE 256

// ADXL345 Configuration
#define ADXL345_ADDR 0x53

// ADXL345 registers
#define REG_DEVID 0x00
#define REG_BW_RATE 0x2C
#define REG_POWER_CTL 0x2D
#define REG_DATA_FORMAT 0x31
#define REG_DATAX0 0x32

static const char *TAG = "SIMPLE_ADXL345";

// Write single byte to register for a specific sensor
static esp_err_t write_reg(i2c_port_t i2c_port, uint8_t reg, uint8_t val)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_write_byte(cmd, val, true);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return err;
}

// Read 6 bytes starting at DATAX0 register for accelerometer data
static esp_err_t read_accel_data(i2c_port_t i2c_port, uint8_t *buf)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    // Send register address
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, REG_DATAX0, true);
    // Read 6 bytes
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read(cmd, buf, 5, I2C_MASTER_ACK);
    i2c_master_read_byte(cmd, buf + 5, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return err;
}

// Read single byte from register
static esp_err_t read_reg(i2c_port_t i2c_port, uint8_t reg, uint8_t *val)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read_byte(cmd, val, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return err;
}

// Initialize a specific I2C bus
static esp_err_t i2c_bus_init(i2c_port_t i2c_port, int sda_io, int scl_io)
{
    i2c_config_t cfg = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = sda_io,
        .scl_io_num = scl_io,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = I2C_MASTER_FREQ_HZ,
    };
    esp_err_t err = i2c_param_config(i2c_port, &cfg);
    if (err != ESP_OK)
        return err;
    return i2c_driver_install(i2c_port, cfg.mode, 0, 0, 0);
}

// Initialize all I2C buses
static esp_err_t i2c_master_init(void)
{
    esp_err_t err;

    // Initialize I2C Bus 0
    err = i2c_bus_init(I2C_BUS_0, I2C_BUS_0_SDA_IO, I2C_BUS_0_SCL_IO);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize I2C Bus 0: %s", esp_err_to_name(err));
        return err;
    }
    ESP_LOGI(TAG, "I2C Bus 0 initialized (SDA=%d, SCL=%d)", I2C_BUS_0_SDA_IO, I2C_BUS_0_SCL_IO);

    // Initialize I2C Bus 1
    err = i2c_bus_init(I2C_BUS_1, I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize I2C Bus 1: %s", esp_err_to_name(err));
        return err;
    }
    ESP_LOGI(TAG, "I2C Bus 1 initialized (SDA=%d, SCL=%d)", I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO);

    return ESP_OK;
}

// Initialize UART for data transmission
static esp_err_t uart_init(void)
{
    uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };

    esp_err_t err = uart_driver_install(UART_PORT_NUM, UART_BUF_SIZE, UART_BUF_SIZE, 0, NULL, 0);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(err));
        return err;
    }

    err = uart_param_config(UART_PORT_NUM, &uart_config);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to configure UART: %s", esp_err_to_name(err));
        return err;
    }

    err = uart_set_pin(UART_PORT_NUM, UART_TX_PIN, UART_RX_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(err));
        return err;
    }

    ESP_LOGI(TAG, "UART initialized: %d baud, TX=%d, RX=%d", UART_BAUD_RATE, UART_TX_PIN, UART_RX_PIN);
    return ESP_OK;
}

// Initialize a specific ADXL345 sensor
static esp_err_t adxl345_init(i2c_port_t i2c_port, uint8_t sensor_id)
{
    uint8_t id = 0;
    esp_err_t err = read_reg(i2c_port, REG_DEVID, &id);

    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to read DEVID from sensor %d: %s", sensor_id, esp_err_to_name(err));
        return ESP_FAIL;
    }

    if (id != 0xE5)
    {
        ESP_LOGE(TAG, "Invalid DEVID on sensor %d: expected 0xE5, got 0x%02X", sensor_id, id);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "ADXL345 sensor %d detected (DEVID=0x%02X)", sensor_id, id);

    // Set output data rate to 100 Hz (simple rate)
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_BW_RATE, 0x0A));
    // ±16 g, full-resolution
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_DATA_FORMAT, 0x0B));
    // Enable measurement mode
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_POWER_CTL, 0x08));

    ESP_LOGI(TAG, "Sensor %d initialized: ±16g @100Hz", sensor_id);
    return ESP_OK;
}

// Send sensor data via UART in simple text format
static void send_sensor_data(uint8_t sensor_id, int16_t x, int16_t y, int16_t z)
{
    char buffer[64];
    int len = snprintf(buffer, sizeof(buffer), "SENSOR%d: X=%d Y=%d Z=%d\n", sensor_id, x, y, z);
    uart_write_bytes(UART_PORT_NUM, buffer, len);
}

void app_main(void)
{
    ESP_LOGI(TAG, "Starting simple dual ADXL345 sensor firmware");

    // Initialize I2C buses
    ESP_ERROR_CHECK(i2c_master_init());

    // Initialize UART for data transmission
    ESP_ERROR_CHECK(uart_init());

    // Initialize both sensors
    ESP_LOGI(TAG, "Initializing sensors...");
    if (adxl345_init(I2C_BUS_0, 1) != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize sensor 1");
        return;
    }

    if (adxl345_init(I2C_BUS_1, 2) != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize sensor 2");
        return;
    }

    ESP_LOGI(TAG, "Both sensors initialized successfully");
    ESP_LOGI(TAG, "Starting main loop - alternating between sensors");

    uint8_t current_sensor = 1;
    uint8_t raw_data[6];
    int16_t x, y, z;

    while (1)
    {
        i2c_port_t i2c_port = (current_sensor == 1) ? I2C_BUS_0 : I2C_BUS_1;

        // Read accelerometer data from current sensor
        esp_err_t err = read_accel_data(i2c_port, raw_data);

        if (err == ESP_OK)
        {
            // Convert raw data to signed 16-bit values
            x = (int16_t)((raw_data[1] << 8) | raw_data[0]);
            y = (int16_t)((raw_data[3] << 8) | raw_data[2]);
            z = (int16_t)((raw_data[5] << 8) | raw_data[4]);

            // Send data via UART
            send_sensor_data(current_sensor, x, y, z);
        }
        else
        {
            ESP_LOGW(TAG, "Failed to read from sensor %d: %s", current_sensor, esp_err_to_name(err));
        }

        // Switch to the other sensor
        current_sensor = (current_sensor == 1) ? 2 : 1;

        // Small delay between readings
        vTaskDelay(pdMS_TO_TICKS(50)); // 20 Hz per sensor, 40 Hz total
    }
}
